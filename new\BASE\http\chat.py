from litellm import acompletion
import json
from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id
import time
import traceback
from ..utils.utils import log_memory_usage
import os
from prompts import PROMPTS
from ..tools.tools_list import get as get_tools_list
from ..tools.execute_tool import execute_tool_call


MESSAGE_DELIMITER = "<__!!__END__!!__>"


def extract_last_user_message_content(payload_messages: list) -> str:
    """Extract the last user message content from payload messages."""
    for message in reversed(payload_messages):
        # Check if message is a user message
        if (isinstance(message, dict) and message.get("role") == "user") or \
           (hasattr(message, "role") and message.role == "user"):
            # Get content from either dict or object
            return message.get("content", "") if isinstance(message, dict) else getattr(message, "content", "")
    return ""

@logger.catch()
async def generate_followup_questions(llm_: dict, last_user_message: str, current_content: str) -> list:
    """Generate follow-up questions based on the last user message and conversation history."""
    try:
        logger.info(f"Generating follow-up questions for message: {last_user_message[:100]}...")
        
        # Get system prompt for follow-up generation
        SYSTEM_PROMPT = await PROMPTS.get("followup_questions")
        
        # Prepare conversation context
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": f"Context of our conversation:\n{current_content}\n\nUser's message: {last_user_message}"},
        ]

        # Configure model
        model_name = llm_.get("model")
        if not model_name.startswith(("openai/", "anthropic/", "cohere/", "huggingface/")):
            model_name = f"openai/{model_name}"

        # Generate questions
        response = await acompletion(
            base_url=llm_.get("base_url", "https://llm.codemate.ai/v1"),
            api_key=llm_.get("api_key", ""),
            model=model_name,
            messages=messages,
            temperature=0.7,
            stream=False
        )

        # Parse XML response
        if hasattr(response, 'choices') and len(response.choices) > 0:
            content = response.choices[0].message.content
            
            # Extract questions from XML format
            questions = []
            if "<questions>" in content and "</questions>" in content:
                xml_content = content.split("<questions>")[1].split("</questions>")[0]
                question_tags = xml_content.split("<question>")
                
                for tag in question_tags[1:]:  # Skip first empty split
                    if "</question>" in tag:
                        question = tag.split("</question>")[0].strip()
                        if question:
                            questions.append(question)
                
                logger.info(f"Generated {len(questions)} follow-up questions")
                return questions[:5]  # Return up to 5 questions
            else:
                logger.warning("Response did not contain proper XML format")
                
        return []

    except Exception as e:
        logger.error(f"Error generating follow-up questions: {e}")
        logger.error(traceback.format_exc())
        return []







async def send_chat_history_to_cloud(conversation_messages: list[dict], session_id: str, is_error: bool = False) -> None:
    """
    Send conversation messages to the cloud /chat/history endpoint.

    Args:
        conversation_messages: List of conversation messages from the current chat session
        session_id: Session identifier for the x-session header
    """
    try:
        if not session_id:
            logger.warning("No session ID provided, skipping chat history sync to cloud")
            return

        if not conversation_messages:
            logger.warning("No conversation messages to send to cloud")
            return

        # For now, just log the messages since cloud integration is commented out
        logger.info(f"Would send chat history to cloud with {len(conversation_messages)} messages")
        logger.info(f"Conversation messages: {json.dumps(conversation_messages, indent=2)}")

        # Prepare the payload with conversation messages
        payload = {
            "messages": conversation_messages,
            "is_error": is_error
        }

        # Prepare headers with session identifier
        headers = {
            "x-session": session_id,
            "Content-Type": "application/json"
        }

        # Make the POST request to cloud
        # async with httpx.AsyncClient(
        #     verify=SSL_CERT_FILE,
        #     timeout=30.0  # 30 second timeout
        # ) as client:
        #     response = await client.post(
        #         endpoint_url,
        #         json=payload,
        #         headers=headers
        #     )

        #     if response.status_code == 200:
        #         logger.info("Successfully sent chat history to cloud")
        #     else:
        #         logger.warning(f"Failed to send chat history to cloud. Status: {response.status_code}, Response: {response.text}")

    except Exception as e:
        logger.error(f"Error sending chat history to cloud: {e}")
        logger.error(f"Chat history sync error traceback: {traceback.format_exc()}")
        # Don't raise the exception to avoid disrupting the main chat flow



        
@logger.catch()
def process_file_context(file_path: str) -> dict:
    """Read and process a file's content.
    
    Args:
        file_path: Path to the file
        context: Dictionary to store file content
    
    Returns:
        Updated context dictionary
    """
    if not file_path:
        logger.warning("No file path provided")
    
    try:
        # Read file content
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
            
        
        
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        content = ""
        
    return content

def truncate_file_content(content: str, max_tokens: int = 15000) -> str:
    """Truncate file content if it exceeds token limit
    
    Args:
        content: File content
        max_tokens: Maximum token limit
        
    Returns:
        Potentially truncated content
    """
    estimated_tokens = estimate_token_count(content)
    
    if estimated_tokens <= max_tokens:
        return content
    
    # Calculate approximate character limit
    char_limit = int((max_tokens / estimated_tokens) * len(content) * 0.9)  # 90% safety margin

    if char_limit < len(content):
        truncated = content[:char_limit]
        truncated += "\n\n[Content truncated due to size limit]"
        logger.warning(f"File content truncated from {len(content)} to {len(truncated)} characters")
        return truncated
    
    return content

def estimate_token_count(text: str) -> int:
            """Rough estimation of token count based on word count"""
            return len(text.split()) * 1.3  # Rough approximation

def process_context(ctx: dict) -> tuple[str, str]:
            """Process individual context and return replacement and additional content"""
            replacement_content = ""
            additional_content = ""
            
            if ctx["type"] == "file":
                content = process_file_context(ctx["path"])
                estimated_tokens = estimate_token_count(f"File Name: {ctx['name']}\nFile Path: {ctx['path']}\nFile Content:\n{content}\n\n")
                
                if estimated_tokens > 15000:
                    logger.warning(f"File {ctx['name']} content too large, may be truncated")
                    content = truncate_file_content(content)
                
                replacement_content = f"File: {ctx['path']}"
                additional_content = (
                    f"========\n"
                    f"File Name: {ctx['name']}\n"
                    f"File Path: {ctx['path']}\n"
                    f"File Content:\n{content}\n"
                    f"========\n"
                )
            elif ctx["type"] == "swagger":
                from .utils import process_swagger_context
                replacement_content, additional_content = process_swagger_context(ctx)

            elif ctx["type"] in ["terminal", "warnings", "errors", "commit"]:
                type_mapping = {
                    "terminal": "Terminal logs",
                    "warnings": "Warnings",
                    "errors": "Errors",
                    "commit": f"Commit {ctx.get('name', '')}"
                }
                replacement_content = type_mapping[ctx["type"]]
                additional_content = f"\n=====\n{type_mapping[ctx['type']]}: {ctx['content']}\n=====\n"
            
            return replacement_content, additional_content
    


async def chat_stream(llm_: dict, messages: list, call_for: str = "chat",  session_id: str = "", ):
    """
    Handle chat streaming requests.

    llm_ :: Dictionary containing LLM configuration. => {
        "base_url": str,  # Base URL for the LLM API
        "api_key": str,  # API key for authentication | Default => session_id for CodeMate Cloud Calls.
        "model": str,  # Model name to use
    }
    messages :: List of messages in the chat.
    messages format :: {
        "role": str, # system, user, assistant, tool_call
        "content": str | dict # Content of the message, can be a string or a dict containing string and image base64,
        "context": list | None # Optional, list of contexts to use for the message
    }
    tools :: List of tools available for the LLM to use.
    """

    has_yielded = False

    try:
        logger.info(f"[stream] Starting chat stream for {llm_.get('model')} with {len(messages)} messages")

        history_messages = messages

        SYSTEM_PROMPT = await PROMPTS.get(call_for)
        sequence_messages = [{
            "role": "system",
            "content": SYSTEM_PROMPT
        }]
        sequence_messages.extend(messages)
        # prepare tools
        # Extract contexts from user messages and get tools

        contexts = []
        for message in sequence_messages:
            if message["role"] == "user" and message.get("context"):
                context = message["context"]
                if not isinstance(context, list):
                    context = [context]
                    
                for ctx in context:
                    replacement, additional = process_context(ctx)
                    ctx["replacement_content"] = replacement
                    ctx["additional_content"] = additional
                    contexts.append(ctx)
                
                del message["context"]

        
        
        tools = get_tools_list(contexts)

        iteration = 0
        logger.info(f"[stream] Starting chat stream for {llm_.get('model')}")

        while True:
            iteration += 1
            # For custom endpoints, we need to prefix the model with openai/ to tell litellm to use OpenAI format
            model_name = llm_.get("model")
            if not model_name.startswith(("openai/", "anthropic/", "cohere/", "huggingface/")):
                model_name = f"openai/{model_name}"

            response = await acompletion(
                base_url=llm_.get("base_url", "https://llm.codemate.ai/v1"),
                api_key=llm_.get("api_key", ""),
                model="gpt-4.1-mini",
                messages=sequence_messages,
                tools=tools if len(tools) != 0 else None,
                tool_choice="auto" if len(tools) != 0 else None,
                temperature=0.4,
                stream=True
            )


            logger.info(f"messages send to llm {json.dumps(sequence_messages, indent=2)}")
            chunks = []
            tool_calls = {}
            current_content = ""
            index = 0

            async for chunk in response:
                try:
                    # Safely access chunk data
                    if hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                        choice = chunk.choices[0]
                        delta = choice.delta if hasattr(choice, 'delta') else None

                        if delta and hasattr(delta, 'tool_calls') and delta.tool_calls:
                            for tool_call in delta.tool_calls:
                                tool_call_index = tool_call.index
                                if tool_call_index not in tool_calls:
                                    tool_calls[tool_call_index] = {"name": "", "args": "", "id": ""}

                                if hasattr(tool_call, 'id') and tool_call.id:
                                    tool_calls[tool_call_index]["id"] = tool_call.id

                                if hasattr(tool_call, 'function') and tool_call.function:
                                    if hasattr(tool_call.function, 'name') and tool_call.function.name:
                                        tool_calls[tool_call_index]["name"] += tool_call.function.name

                                    if hasattr(tool_call.function, 'arguments') and tool_call.function.arguments:
                                        tool_calls[tool_call_index]["args"] += tool_call.function.arguments

                        if delta and hasattr(delta, 'content') and delta.content:
                            current_content += delta.content
                            has_yielded = True
                            yield json.dumps({
                                "index": index,
                                "type": "message",
                                "message": delta.content
                            }) + MESSAGE_DELIMITER

                except Exception as chunk_error:
                    logger.warning(f"Error processing chunk {index}: {str(chunk_error)}")
                    continue
                finally:
                    index += 1

            if current_content.strip() and not tool_calls:
                logger.info(f"[stream] Conversation completed after {iteration} iterations")
                break

            if tool_calls:
                logger.info(f"[stream] Processing {len(tool_calls)} tool calls")

                # Add assistant message with tool calls to sequence
                assistant_message = {
                    "role": "assistant",
                    "content": current_content,
                    "tool_calls": []
                }

                tool_results = []

                for tool_call_index, func_data in tool_calls.items():
                    if func_data["name"] and func_data["args"]:
                        # Yield tool call information
                        has_yielded = True
                        yield json.dumps({
                            "index": tool_call_index,
                            "type": "tool_call",
                            "tool_call": {
                                "name": func_data["name"],
                                "arguments": func_data["args"]
                            }
                        }) + MESSAGE_DELIMITER

                        # Add to assistant message
                        assistant_message["tool_calls"].append({
                            "id": func_data["id"] or f"call_{tool_call_index}",
                            "type": "function",
                            "function": {
                                "name": func_data["name"],
                                "arguments": func_data["args"]
                            }
                        })

                        # Execute the tool call
                        tool_call_data = {
                            "id": func_data["id"] or f"call_{tool_call_index}",
                            "function": {
                                "name": func_data["name"],
                                "arguments": func_data["args"]
                            }
                        }

                        try:
                            tool_result, _ = await execute_tool_call(tool_call_data)

                            # Add tool result to conversation
                            tool_results.append({
                                "role": "tool",
                                "tool_call_id": tool_result["tool_call_id"],
                                "content": tool_result["content"]
                            })

                        except Exception as tool_error:
                            logger.error(f"Error executing tool call: {tool_error}")
                            tool_results.append({
                                "role": "tool",
                                "tool_call_id": func_data["id"] or f"call_{tool_call_index}",
                                "content": json.dumps({
                                    "error": str(tool_error),
                                    "status": "error"
                                })
                            })

                # Add assistant message and tool results to sequence
                sequence_messages.append(assistant_message)
                sequence_messages.extend(tool_results)
                history_messages.extend(tool_results)

                # Continue the conversation loop to get LLM response to tool results
                continue

            # If no content and no tool calls, break to avoid infinite loop
            if not current_content.strip() and not tool_calls:
                logger.info(f"[stream] No content or tool calls, ending conversation")
                break

        # Ensure we always yield something
        if not has_yielded:
            yield json.dumps({
                "index": 0,
                "type": "message",
                "message": "No response generated"
            }) + MESSAGE_DELIMITER

        # Handle follow-up functionality after successful response completion
        if True:
            try:
                # Extract last user message content
                last_user_message_content = extract_last_user_message_content(messages,  [])

                if last_user_message_content:
                    logger.info(f"Processing follow-ups for last user message: {last_user_message_content[:100]}...")

                    # Generate follow-up questions
                    followup_questions = await generate_followup_questions(llm_, last_user_message_content, current_content)

                    if followup_questions:
                        # Process follow-up chat
                        for question in followup_questions:
                            yield json.dumps({
                                "index": 0,
                                "type": "follow_up_question",
                                "message": question
                            }) + MESSAGE_DELIMITER

                        logger.info(f"Successfully processed {len(followup_questions)} follow-up questions")
                    else:
                        logger.info("No follow-up questions generated")
                else:
                    logger.warning("No last user message content found for follow-ups")

            except Exception as followup_error:
                logger.error(f"Error processing follow-ups: {followup_error}")
                # Don't let follow-up errors affect the main response

            try:
                send_chat_history_to_cloud(history_messages, session_id)
            except Exception as cloud_error:
                logger.error(f"Error sending chat history to cloud: {cloud_error}")

            yield json.dumps({
                "index": 0,
                "type": "end",
                "message": "End of conversation"
            }) + MESSAGE_DELIMITER

        logger.debug(message=f"Chat stream completed for {llm_.get('model')}", extra={
            "_log_": "chat_stream",
            "messages": messages,
            "llm_": llm_,
            "chunks_returned": chunks
        })



    except Exception as e:
        logger.error(f"Error in chat_stream: {str(e)}")
        yield json.dumps({
            "index": 0,
            "type": "error",
            "message": f"Error: {str(e)}"
        }) + MESSAGE_DELIMITER